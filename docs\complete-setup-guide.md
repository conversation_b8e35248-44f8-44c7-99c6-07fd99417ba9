# Google Analytics MCP 完整配置指南

## 📋 概述

本指南将详细说明如何从零开始配置Google Analytics MCP服务，让AI助手能够直接查询您的Google Analytics数据。

## 🎯 最终目标

配置完成后，您可以直接询问AI：
- "昨天有多少用户访问了网站？"
- "过去7天的流量趋势如何？"
- "主要流量来源是什么？"

AI将自动调用Google Analytics API获取准确数据。

## 📁 项目结构

```
google-analytics-mcp/
├── docs/                          # 文档目录
├── official-ga-mcp/               # 官方MCP服务器代码
├── client_secret_*.json           # OAuth客户端密钥文件
├── complete_reauth.py             # 认证脚本
├── setup-analytics-mcp.bat        # 一键配置脚本
├── auth-helper.bat                # 认证助手脚本
└── test_mcp_simple.py             # 功能测试脚本
```

## 🚀 快速开始（推荐）

### 方法一：一键配置
```batch
# 双击运行完整配置
setup-analytics-mcp.bat
```

### 方法二：仅认证配置
```batch
# 双击运行认证助手
auth-helper.bat
```

## 📖 详细配置步骤

### 第一步：环境准备

#### 1.1 检查Python环境
```bash
python --version
# 需要Python 3.8+
```

#### 1.2 激活虚拟环境（如果有）
```bash
# Windows
.venv\Scripts\activate

# Linux/macOS
source .venv/bin/activate
```

#### 1.3 安装必要的Python包
```bash
pip install google-auth google-auth-oauthlib google-analytics-data google-analytics-admin
```

### 第二步：Google Cloud项目配置

#### 2.1 创建Google Cloud项目
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 记录项目ID（如：`sound-silicon-469508-a8`）

#### 2.2 启用必要的API
访问以下链接启用API：
- [Google Analytics Admin API](https://console.cloud.google.com/apis/library/analyticsadmin.googleapis.com)
- [Google Analytics Data API](https://console.cloud.google.com/apis/library/analyticsdata.googleapis.com)

#### 2.3 创建OAuth 2.0客户端
1. 访问 [凭据页面](https://console.cloud.google.com/apis/credentials)
2. 点击"创建凭据" → "OAuth 2.0客户端ID"
3. 应用类型选择"桌面应用程序"
4. 下载JSON文件，重命名为 `client_secret_*.json`
5. 将文件放在项目根目录

### 第三步：认证配置

#### 3.1 运行认证脚本
```bash
python complete_reauth.py
```

#### 3.2 完成浏览器认证
1. 脚本会自动打开浏览器认证页面
2. 登录您的Google账户
3. 授权应用访问Google Analytics
4. 复制浏览器显示的授权码
5. 回到命令行粘贴授权码

#### 3.3 验证认证成功
看到以下信息表示成功：
```
✅ 认证成功！
✅ 凭据已保存到: C:\Users\<USER>\AppData\Roaming\gcloud\application_default_credentials.json
```

### 第四步：MCP服务器配置

#### 4.1 安装MCP服务器
```bash
# 方法一：从GitHub安装
pip install git+https://github.com/googleanalytics/google-analytics-mcp.git

# 方法二：本地安装
cd official-ga-mcp
pip install -e .
```

#### 4.2 测试MCP功能
```bash
python test_mcp_simple.py
```

成功输出：
```
Testing Google Analytics package import...
OK: Package import successful
Testing client creation...
OK: Client creation successful

SUCCESS: Google Analytics MCP configuration completed!
```

### 第五步：AI客户端配置

#### 5.1 Claude Desktop配置
创建或编辑 `~/.config/claude/claude_desktop_config.json`：

```json
{
  "mcpServers": {
    "analytics-mcp": {
      "command": "google-analytics-mcp",
      "args": [],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "C:\\Users\\<USER>\\AppData\\Roaming\\gcloud\\application_default_credentials.json",
        "GOOGLE_CLOUD_PROJECT": "your-project-id"
      }
    }
  }
}
```

#### 5.2 其他MCP客户端配置
根据您使用的AI客户端，参考相应的MCP配置文档。

## 🧪 功能测试

### 测试基础功能
询问AI以下问题来测试功能：

1. **获取账户信息**
   ```
   "显示我的Google Analytics账户和属性"
   ```

2. **查询用户数据**
   ```
   "昨天有多少用户访问了网站？"
   ```

3. **流量趋势分析**
   ```
   "过去7天的流量趋势如何？"
   ```

4. **实时数据**
   ```
   "现在有多少用户在线？"
   ```

### 预期结果
AI应该能够：
- ✅ 自动调用Google Analytics API
- ✅ 获取准确的数据
- ✅ 生成详细的分析报告
- ✅ 回答各种数据相关问题

## ❌ 常见问题解决

### 权限不足错误
```
403 ACCESS_TOKEN_SCOPE_INSUFFICIENT
```
**解决方案**：重新运行认证脚本
```bash
python complete_reauth.py
```

### API配额限制
```
429 Exhausted property tokens per day
```
**解决方案**：
- 等待24小时配额重置
- 使用不同的Google Analytics属性测试

### 授权码无效
```
Invalid authorization code
```
**解决方案**：
- 确保完整复制授权码
- 检查是否有多余空格
- 重新获取授权码

### MCP服务器未找到
**解决方案**：
1. 检查MCP服务器是否正确安装
2. 验证配置文件路径
3. 重启AI客户端

## 🔧 高级配置

### 自定义权限范围
如需更多权限，修改 `complete_reauth.py` 中的 `SCOPES`：
```python
SCOPES = [
    'https://www.googleapis.com/auth/analytics.readonly',
    'https://www.googleapis.com/auth/cloud-platform',
    'https://www.googleapis.com/auth/analytics.manage.users.readonly',
    'https://www.googleapis.com/auth/analytics.edit'  # 添加编辑权限
]
```

### 多项目配置
为不同项目创建不同的认证文件：
```bash
# 项目A
GOOGLE_CLOUD_PROJECT=project-a python complete_reauth.py

# 项目B  
GOOGLE_CLOUD_PROJECT=project-b python complete_reauth.py
```

## 📊 可用的MCP工具

配置完成后，以下工具将可用：

### 账户管理
- `get_account_summaries_analytics()` - 获取账户和属性信息
- `get_property_details_analytics()` - 获取属性详细信息
- `list_google_ads_links_analytics()` - 查看Google Ads链接

### 数据报告
- `run_report_analytics()` - 生成标准数据报告
- `run_realtime_report_analytics()` - 获取实时数据
- `get_custom_dimensions_and_metrics_analytics()` - 查看自定义维度和指标

## 🎉 配置完成

恭喜！您的Google Analytics MCP服务现在已经完全配置并可以使用了！

现在您可以：
- 📊 直接询问AI关于网站数据的任何问题
- 📈 获取实时准确的Google Analytics数据
- 🔍 进行深度数据分析和趋势预测
- 📋 生成各种自定义报告

**开始享受AI驱动的数据分析体验吧！** 🚀
