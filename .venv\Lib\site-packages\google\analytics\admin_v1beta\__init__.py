# -*- coding: utf-8 -*-
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from google.analytics.admin_v1beta import gapic_version as package_version

__version__ = package_version.__version__


from .services.analytics_admin_service import (
    AnalyticsAdminServiceAsyncClient,
    AnalyticsAdminServiceClient,
)
from .types.access_report import (
    AccessBetweenFilter,
    AccessDateRange,
    AccessDimension,
    AccessDimensionHeader,
    AccessDimensionValue,
    AccessFilter,
    AccessFilterExpression,
    AccessFilterExpressionList,
    AccessInListFilter,
    AccessMetric,
    AccessMetricHeader,
    AccessMetricValue,
    AccessNumericFilter,
    AccessOrderBy,
    AccessQuota,
    AccessQuotaStatus,
    AccessRow,
    AccessStringFilter,
    NumericValue,
)
from .types.analytics_admin import (
    AcknowledgeUserDataCollectionRequest,
    AcknowledgeUserDataCollectionResponse,
    ArchiveCustomDimensionRequest,
    ArchiveCustomMetricRequest,
    CreateConversionEventRequest,
    CreateCustomDimensionRequest,
    CreateCustomMetricRequest,
    CreateDataStreamRequest,
    CreateFirebaseLinkRequest,
    CreateGoogleAdsLinkRequest,
    CreateKeyEventRequest,
    CreateMeasurementProtocolSecretRequest,
    CreatePropertyRequest,
    DeleteAccountRequest,
    DeleteConversionEventRequest,
    DeleteDataStreamRequest,
    DeleteFirebaseLinkRequest,
    DeleteGoogleAdsLinkRequest,
    DeleteKeyEventRequest,
    DeleteMeasurementProtocolSecretRequest,
    DeletePropertyRequest,
    GetAccountRequest,
    GetConversionEventRequest,
    GetCustomDimensionRequest,
    GetCustomMetricRequest,
    GetDataRetentionSettingsRequest,
    GetDataSharingSettingsRequest,
    GetDataStreamRequest,
    GetKeyEventRequest,
    GetMeasurementProtocolSecretRequest,
    GetPropertyRequest,
    ListAccountsRequest,
    ListAccountsResponse,
    ListAccountSummariesRequest,
    ListAccountSummariesResponse,
    ListConversionEventsRequest,
    ListConversionEventsResponse,
    ListCustomDimensionsRequest,
    ListCustomDimensionsResponse,
    ListCustomMetricsRequest,
    ListCustomMetricsResponse,
    ListDataStreamsRequest,
    ListDataStreamsResponse,
    ListFirebaseLinksRequest,
    ListFirebaseLinksResponse,
    ListGoogleAdsLinksRequest,
    ListGoogleAdsLinksResponse,
    ListKeyEventsRequest,
    ListKeyEventsResponse,
    ListMeasurementProtocolSecretsRequest,
    ListMeasurementProtocolSecretsResponse,
    ListPropertiesRequest,
    ListPropertiesResponse,
    ProvisionAccountTicketRequest,
    ProvisionAccountTicketResponse,
    RunAccessReportRequest,
    RunAccessReportResponse,
    SearchChangeHistoryEventsRequest,
    SearchChangeHistoryEventsResponse,
    UpdateAccountRequest,
    UpdateConversionEventRequest,
    UpdateCustomDimensionRequest,
    UpdateCustomMetricRequest,
    UpdateDataRetentionSettingsRequest,
    UpdateDataStreamRequest,
    UpdateGoogleAdsLinkRequest,
    UpdateKeyEventRequest,
    UpdateMeasurementProtocolSecretRequest,
    UpdatePropertyRequest,
)
from .types.resources import (
    Account,
    AccountSummary,
    ActionType,
    ActorType,
    ChangeHistoryChange,
    ChangeHistoryEvent,
    ChangeHistoryResourceType,
    ConversionEvent,
    CustomDimension,
    CustomMetric,
    DataRetentionSettings,
    DataSharingSettings,
    DataStream,
    FirebaseLink,
    GoogleAdsLink,
    IndustryCategory,
    KeyEvent,
    MeasurementProtocolSecret,
    Property,
    PropertySummary,
    PropertyType,
    ServiceLevel,
)

__all__ = (
    "AnalyticsAdminServiceAsyncClient",
    "AccessBetweenFilter",
    "AccessDateRange",
    "AccessDimension",
    "AccessDimensionHeader",
    "AccessDimensionValue",
    "AccessFilter",
    "AccessFilterExpression",
    "AccessFilterExpressionList",
    "AccessInListFilter",
    "AccessMetric",
    "AccessMetricHeader",
    "AccessMetricValue",
    "AccessNumericFilter",
    "AccessOrderBy",
    "AccessQuota",
    "AccessQuotaStatus",
    "AccessRow",
    "AccessStringFilter",
    "Account",
    "AccountSummary",
    "AcknowledgeUserDataCollectionRequest",
    "AcknowledgeUserDataCollectionResponse",
    "ActionType",
    "ActorType",
    "AnalyticsAdminServiceClient",
    "ArchiveCustomDimensionRequest",
    "ArchiveCustomMetricRequest",
    "ChangeHistoryChange",
    "ChangeHistoryEvent",
    "ChangeHistoryResourceType",
    "ConversionEvent",
    "CreateConversionEventRequest",
    "CreateCustomDimensionRequest",
    "CreateCustomMetricRequest",
    "CreateDataStreamRequest",
    "CreateFirebaseLinkRequest",
    "CreateGoogleAdsLinkRequest",
    "CreateKeyEventRequest",
    "CreateMeasurementProtocolSecretRequest",
    "CreatePropertyRequest",
    "CustomDimension",
    "CustomMetric",
    "DataRetentionSettings",
    "DataSharingSettings",
    "DataStream",
    "DeleteAccountRequest",
    "DeleteConversionEventRequest",
    "DeleteDataStreamRequest",
    "DeleteFirebaseLinkRequest",
    "DeleteGoogleAdsLinkRequest",
    "DeleteKeyEventRequest",
    "DeleteMeasurementProtocolSecretRequest",
    "DeletePropertyRequest",
    "FirebaseLink",
    "GetAccountRequest",
    "GetConversionEventRequest",
    "GetCustomDimensionRequest",
    "GetCustomMetricRequest",
    "GetDataRetentionSettingsRequest",
    "GetDataSharingSettingsRequest",
    "GetDataStreamRequest",
    "GetKeyEventRequest",
    "GetMeasurementProtocolSecretRequest",
    "GetPropertyRequest",
    "GoogleAdsLink",
    "IndustryCategory",
    "KeyEvent",
    "ListAccountSummariesRequest",
    "ListAccountSummariesResponse",
    "ListAccountsRequest",
    "ListAccountsResponse",
    "ListConversionEventsRequest",
    "ListConversionEventsResponse",
    "ListCustomDimensionsRequest",
    "ListCustomDimensionsResponse",
    "ListCustomMetricsRequest",
    "ListCustomMetricsResponse",
    "ListDataStreamsRequest",
    "ListDataStreamsResponse",
    "ListFirebaseLinksRequest",
    "ListFirebaseLinksResponse",
    "ListGoogleAdsLinksRequest",
    "ListGoogleAdsLinksResponse",
    "ListKeyEventsRequest",
    "ListKeyEventsResponse",
    "ListMeasurementProtocolSecretsRequest",
    "ListMeasurementProtocolSecretsResponse",
    "ListPropertiesRequest",
    "ListPropertiesResponse",
    "MeasurementProtocolSecret",
    "NumericValue",
    "Property",
    "PropertySummary",
    "PropertyType",
    "ProvisionAccountTicketRequest",
    "ProvisionAccountTicketResponse",
    "RunAccessReportRequest",
    "RunAccessReportResponse",
    "SearchChangeHistoryEventsRequest",
    "SearchChangeHistoryEventsResponse",
    "ServiceLevel",
    "UpdateAccountRequest",
    "UpdateConversionEventRequest",
    "UpdateCustomDimensionRequest",
    "UpdateCustomMetricRequest",
    "UpdateDataRetentionSettingsRequest",
    "UpdateDataStreamRequest",
    "UpdateGoogleAdsLinkRequest",
    "UpdateKeyEventRequest",
    "UpdateMeasurementProtocolSecretRequest",
    "UpdatePropertyRequest",
)
