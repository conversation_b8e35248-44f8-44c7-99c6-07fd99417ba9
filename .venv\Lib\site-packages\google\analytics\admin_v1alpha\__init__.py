# -*- coding: utf-8 -*-
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from google.analytics.admin_v1alpha import gapic_version as package_version

__version__ = package_version.__version__


from .services.analytics_admin_service import (
    AnalyticsAdminServiceAsyncClient,
    AnalyticsAdminServiceClient,
)
from .types.access_report import (
    AccessBetweenFilter,
    AccessDateRange,
    AccessDimension,
    AccessDimensionHeader,
    AccessDimensionValue,
    AccessFilter,
    AccessFilterExpression,
    AccessFilterExpressionList,
    AccessInListFilter,
    AccessMetric,
    AccessMetricHeader,
    AccessMetricValue,
    AccessNumericFilter,
    AccessOrderBy,
    AccessQuota,
    AccessQuotaStatus,
    AccessRow,
    AccessStringFilter,
    NumericValue,
)
from .types.analytics_admin import (
    AcknowledgeUserDataCollectionRequest,
    AcknowledgeUserDataCollectionResponse,
    ApproveDisplayVideo360AdvertiserLinkProposalRequest,
    ApproveDisplayVideo360AdvertiserLinkProposalResponse,
    ArchiveAudienceRequest,
    ArchiveCustomDimensionRequest,
    ArchiveCustomMetricRequest,
    BatchCreateAccessBindingsRequest,
    BatchCreateAccessBindingsResponse,
    BatchDeleteAccessBindingsRequest,
    BatchGetAccessBindingsRequest,
    BatchGetAccessBindingsResponse,
    BatchUpdateAccessBindingsRequest,
    BatchUpdateAccessBindingsResponse,
    CancelDisplayVideo360AdvertiserLinkProposalRequest,
    CreateAccessBindingRequest,
    CreateAdSenseLinkRequest,
    CreateAudienceRequest,
    CreateBigQueryLinkRequest,
    CreateCalculatedMetricRequest,
    CreateChannelGroupRequest,
    CreateConnectedSiteTagRequest,
    CreateConnectedSiteTagResponse,
    CreateConversionEventRequest,
    CreateCustomDimensionRequest,
    CreateCustomMetricRequest,
    CreateDataStreamRequest,
    CreateDisplayVideo360AdvertiserLinkProposalRequest,
    CreateDisplayVideo360AdvertiserLinkRequest,
    CreateEventCreateRuleRequest,
    CreateEventEditRuleRequest,
    CreateExpandedDataSetRequest,
    CreateFirebaseLinkRequest,
    CreateGoogleAdsLinkRequest,
    CreateKeyEventRequest,
    CreateMeasurementProtocolSecretRequest,
    CreatePropertyRequest,
    CreateReportingDataAnnotationRequest,
    CreateRollupPropertyRequest,
    CreateRollupPropertyResponse,
    CreateRollupPropertySourceLinkRequest,
    CreateSearchAds360LinkRequest,
    CreateSKAdNetworkConversionValueSchemaRequest,
    CreateSubpropertyEventFilterRequest,
    DeleteAccessBindingRequest,
    DeleteAccountRequest,
    DeleteAdSenseLinkRequest,
    DeleteBigQueryLinkRequest,
    DeleteCalculatedMetricRequest,
    DeleteChannelGroupRequest,
    DeleteConnectedSiteTagRequest,
    DeleteConversionEventRequest,
    DeleteDataStreamRequest,
    DeleteDisplayVideo360AdvertiserLinkProposalRequest,
    DeleteDisplayVideo360AdvertiserLinkRequest,
    DeleteEventCreateRuleRequest,
    DeleteEventEditRuleRequest,
    DeleteExpandedDataSetRequest,
    DeleteFirebaseLinkRequest,
    DeleteGoogleAdsLinkRequest,
    DeleteKeyEventRequest,
    DeleteMeasurementProtocolSecretRequest,
    DeletePropertyRequest,
    DeleteReportingDataAnnotationRequest,
    DeleteRollupPropertySourceLinkRequest,
    DeleteSearchAds360LinkRequest,
    DeleteSKAdNetworkConversionValueSchemaRequest,
    DeleteSubpropertyEventFilterRequest,
    FetchAutomatedGa4ConfigurationOptOutRequest,
    FetchAutomatedGa4ConfigurationOptOutResponse,
    FetchConnectedGa4PropertyRequest,
    FetchConnectedGa4PropertyResponse,
    GetAccessBindingRequest,
    GetAccountRequest,
    GetAdSenseLinkRequest,
    GetAttributionSettingsRequest,
    GetAudienceRequest,
    GetBigQueryLinkRequest,
    GetCalculatedMetricRequest,
    GetChannelGroupRequest,
    GetConversionEventRequest,
    GetCustomDimensionRequest,
    GetCustomMetricRequest,
    GetDataRedactionSettingsRequest,
    GetDataRetentionSettingsRequest,
    GetDataSharingSettingsRequest,
    GetDataStreamRequest,
    GetDisplayVideo360AdvertiserLinkProposalRequest,
    GetDisplayVideo360AdvertiserLinkRequest,
    GetEnhancedMeasurementSettingsRequest,
    GetEventCreateRuleRequest,
    GetEventEditRuleRequest,
    GetExpandedDataSetRequest,
    GetGlobalSiteTagRequest,
    GetGoogleSignalsSettingsRequest,
    GetKeyEventRequest,
    GetMeasurementProtocolSecretRequest,
    GetPropertyRequest,
    GetReportingDataAnnotationRequest,
    GetRollupPropertySourceLinkRequest,
    GetSearchAds360LinkRequest,
    GetSKAdNetworkConversionValueSchemaRequest,
    GetSubpropertyEventFilterRequest,
    GetSubpropertySyncConfigRequest,
    ListAccessBindingsRequest,
    ListAccessBindingsResponse,
    ListAccountsRequest,
    ListAccountsResponse,
    ListAccountSummariesRequest,
    ListAccountSummariesResponse,
    ListAdSenseLinksRequest,
    ListAdSenseLinksResponse,
    ListAudiencesRequest,
    ListAudiencesResponse,
    ListBigQueryLinksRequest,
    ListBigQueryLinksResponse,
    ListCalculatedMetricsRequest,
    ListCalculatedMetricsResponse,
    ListChannelGroupsRequest,
    ListChannelGroupsResponse,
    ListConnectedSiteTagsRequest,
    ListConnectedSiteTagsResponse,
    ListConversionEventsRequest,
    ListConversionEventsResponse,
    ListCustomDimensionsRequest,
    ListCustomDimensionsResponse,
    ListCustomMetricsRequest,
    ListCustomMetricsResponse,
    ListDataStreamsRequest,
    ListDataStreamsResponse,
    ListDisplayVideo360AdvertiserLinkProposalsRequest,
    ListDisplayVideo360AdvertiserLinkProposalsResponse,
    ListDisplayVideo360AdvertiserLinksRequest,
    ListDisplayVideo360AdvertiserLinksResponse,
    ListEventCreateRulesRequest,
    ListEventCreateRulesResponse,
    ListEventEditRulesRequest,
    ListEventEditRulesResponse,
    ListExpandedDataSetsRequest,
    ListExpandedDataSetsResponse,
    ListFirebaseLinksRequest,
    ListFirebaseLinksResponse,
    ListGoogleAdsLinksRequest,
    ListGoogleAdsLinksResponse,
    ListKeyEventsRequest,
    ListKeyEventsResponse,
    ListMeasurementProtocolSecretsRequest,
    ListMeasurementProtocolSecretsResponse,
    ListPropertiesRequest,
    ListPropertiesResponse,
    ListReportingDataAnnotationsRequest,
    ListReportingDataAnnotationsResponse,
    ListRollupPropertySourceLinksRequest,
    ListRollupPropertySourceLinksResponse,
    ListSearchAds360LinksRequest,
    ListSearchAds360LinksResponse,
    ListSKAdNetworkConversionValueSchemasRequest,
    ListSKAdNetworkConversionValueSchemasResponse,
    ListSubpropertyEventFiltersRequest,
    ListSubpropertyEventFiltersResponse,
    ListSubpropertySyncConfigsRequest,
    ListSubpropertySyncConfigsResponse,
    ProvisionAccountTicketRequest,
    ProvisionAccountTicketResponse,
    ProvisionSubpropertyRequest,
    ProvisionSubpropertyResponse,
    ReorderEventEditRulesRequest,
    RunAccessReportRequest,
    RunAccessReportResponse,
    SearchChangeHistoryEventsRequest,
    SearchChangeHistoryEventsResponse,
    SetAutomatedGa4ConfigurationOptOutRequest,
    SetAutomatedGa4ConfigurationOptOutResponse,
    SubmitUserDeletionRequest,
    SubmitUserDeletionResponse,
    UpdateAccessBindingRequest,
    UpdateAccountRequest,
    UpdateAttributionSettingsRequest,
    UpdateAudienceRequest,
    UpdateBigQueryLinkRequest,
    UpdateCalculatedMetricRequest,
    UpdateChannelGroupRequest,
    UpdateConversionEventRequest,
    UpdateCustomDimensionRequest,
    UpdateCustomMetricRequest,
    UpdateDataRedactionSettingsRequest,
    UpdateDataRetentionSettingsRequest,
    UpdateDataStreamRequest,
    UpdateDisplayVideo360AdvertiserLinkRequest,
    UpdateEnhancedMeasurementSettingsRequest,
    UpdateEventCreateRuleRequest,
    UpdateEventEditRuleRequest,
    UpdateExpandedDataSetRequest,
    UpdateGoogleAdsLinkRequest,
    UpdateGoogleSignalsSettingsRequest,
    UpdateKeyEventRequest,
    UpdateMeasurementProtocolSecretRequest,
    UpdatePropertyRequest,
    UpdateReportingDataAnnotationRequest,
    UpdateSearchAds360LinkRequest,
    UpdateSKAdNetworkConversionValueSchemaRequest,
    UpdateSubpropertyEventFilterRequest,
    UpdateSubpropertySyncConfigRequest,
)
from .types.audience import (
    Audience,
    AudienceDimensionOrMetricFilter,
    AudienceEventFilter,
    AudienceEventTrigger,
    AudienceFilterClause,
    AudienceFilterExpression,
    AudienceFilterExpressionList,
    AudienceFilterScope,
    AudienceSequenceFilter,
    AudienceSimpleFilter,
)
from .types.channel_group import (
    ChannelGroup,
    ChannelGroupFilter,
    ChannelGroupFilterExpression,
    ChannelGroupFilterExpressionList,
    GroupingRule,
)
from .types.event_create_and_edit import (
    EventCreateRule,
    EventEditRule,
    MatchingCondition,
    ParameterMutation,
)
from .types.expanded_data_set import (
    ExpandedDataSet,
    ExpandedDataSetFilter,
    ExpandedDataSetFilterExpression,
    ExpandedDataSetFilterExpressionList,
)
from .types.resources import (
    AccessBinding,
    Account,
    AccountSummary,
    ActionType,
    ActorType,
    AdSenseLink,
    AttributionSettings,
    BigQueryLink,
    CalculatedMetric,
    ChangeHistoryChange,
    ChangeHistoryEvent,
    ChangeHistoryResourceType,
    CoarseValue,
    ConnectedSiteTag,
    ConversionEvent,
    ConversionValues,
    CustomDimension,
    CustomMetric,
    DataRedactionSettings,
    DataRetentionSettings,
    DataSharingSettings,
    DataStream,
    DisplayVideo360AdvertiserLink,
    DisplayVideo360AdvertiserLinkProposal,
    EnhancedMeasurementSettings,
    EventMapping,
    FirebaseLink,
    GlobalSiteTag,
    GoogleAdsLink,
    GoogleSignalsConsent,
    GoogleSignalsSettings,
    GoogleSignalsState,
    IndustryCategory,
    KeyEvent,
    LinkProposalInitiatingProduct,
    LinkProposalState,
    LinkProposalStatusDetails,
    MeasurementProtocolSecret,
    PostbackWindow,
    Property,
    PropertySummary,
    PropertyType,
    ReportingDataAnnotation,
    RollupPropertySourceLink,
    SearchAds360Link,
    ServiceLevel,
    SKAdNetworkConversionValueSchema,
    SubpropertySyncConfig,
)
from .types.subproperty_event_filter import (
    SubpropertyEventFilter,
    SubpropertyEventFilterClause,
    SubpropertyEventFilterCondition,
    SubpropertyEventFilterExpression,
    SubpropertyEventFilterExpressionList,
)

__all__ = (
    "AnalyticsAdminServiceAsyncClient",
    "AccessBetweenFilter",
    "AccessBinding",
    "AccessDateRange",
    "AccessDimension",
    "AccessDimensionHeader",
    "AccessDimensionValue",
    "AccessFilter",
    "AccessFilterExpression",
    "AccessFilterExpressionList",
    "AccessInListFilter",
    "AccessMetric",
    "AccessMetricHeader",
    "AccessMetricValue",
    "AccessNumericFilter",
    "AccessOrderBy",
    "AccessQuota",
    "AccessQuotaStatus",
    "AccessRow",
    "AccessStringFilter",
    "Account",
    "AccountSummary",
    "AcknowledgeUserDataCollectionRequest",
    "AcknowledgeUserDataCollectionResponse",
    "ActionType",
    "ActorType",
    "AdSenseLink",
    "AnalyticsAdminServiceClient",
    "ApproveDisplayVideo360AdvertiserLinkProposalRequest",
    "ApproveDisplayVideo360AdvertiserLinkProposalResponse",
    "ArchiveAudienceRequest",
    "ArchiveCustomDimensionRequest",
    "ArchiveCustomMetricRequest",
    "AttributionSettings",
    "Audience",
    "AudienceDimensionOrMetricFilter",
    "AudienceEventFilter",
    "AudienceEventTrigger",
    "AudienceFilterClause",
    "AudienceFilterExpression",
    "AudienceFilterExpressionList",
    "AudienceFilterScope",
    "AudienceSequenceFilter",
    "AudienceSimpleFilter",
    "BatchCreateAccessBindingsRequest",
    "BatchCreateAccessBindingsResponse",
    "BatchDeleteAccessBindingsRequest",
    "BatchGetAccessBindingsRequest",
    "BatchGetAccessBindingsResponse",
    "BatchUpdateAccessBindingsRequest",
    "BatchUpdateAccessBindingsResponse",
    "BigQueryLink",
    "CalculatedMetric",
    "CancelDisplayVideo360AdvertiserLinkProposalRequest",
    "ChangeHistoryChange",
    "ChangeHistoryEvent",
    "ChangeHistoryResourceType",
    "ChannelGroup",
    "ChannelGroupFilter",
    "ChannelGroupFilterExpression",
    "ChannelGroupFilterExpressionList",
    "CoarseValue",
    "ConnectedSiteTag",
    "ConversionEvent",
    "ConversionValues",
    "CreateAccessBindingRequest",
    "CreateAdSenseLinkRequest",
    "CreateAudienceRequest",
    "CreateBigQueryLinkRequest",
    "CreateCalculatedMetricRequest",
    "CreateChannelGroupRequest",
    "CreateConnectedSiteTagRequest",
    "CreateConnectedSiteTagResponse",
    "CreateConversionEventRequest",
    "CreateCustomDimensionRequest",
    "CreateCustomMetricRequest",
    "CreateDataStreamRequest",
    "CreateDisplayVideo360AdvertiserLinkProposalRequest",
    "CreateDisplayVideo360AdvertiserLinkRequest",
    "CreateEventCreateRuleRequest",
    "CreateEventEditRuleRequest",
    "CreateExpandedDataSetRequest",
    "CreateFirebaseLinkRequest",
    "CreateGoogleAdsLinkRequest",
    "CreateKeyEventRequest",
    "CreateMeasurementProtocolSecretRequest",
    "CreatePropertyRequest",
    "CreateReportingDataAnnotationRequest",
    "CreateRollupPropertyRequest",
    "CreateRollupPropertyResponse",
    "CreateRollupPropertySourceLinkRequest",
    "CreateSKAdNetworkConversionValueSchemaRequest",
    "CreateSearchAds360LinkRequest",
    "CreateSubpropertyEventFilterRequest",
    "CustomDimension",
    "CustomMetric",
    "DataRedactionSettings",
    "DataRetentionSettings",
    "DataSharingSettings",
    "DataStream",
    "DeleteAccessBindingRequest",
    "DeleteAccountRequest",
    "DeleteAdSenseLinkRequest",
    "DeleteBigQueryLinkRequest",
    "DeleteCalculatedMetricRequest",
    "DeleteChannelGroupRequest",
    "DeleteConnectedSiteTagRequest",
    "DeleteConversionEventRequest",
    "DeleteDataStreamRequest",
    "DeleteDisplayVideo360AdvertiserLinkProposalRequest",
    "DeleteDisplayVideo360AdvertiserLinkRequest",
    "DeleteEventCreateRuleRequest",
    "DeleteEventEditRuleRequest",
    "DeleteExpandedDataSetRequest",
    "DeleteFirebaseLinkRequest",
    "DeleteGoogleAdsLinkRequest",
    "DeleteKeyEventRequest",
    "DeleteMeasurementProtocolSecretRequest",
    "DeletePropertyRequest",
    "DeleteReportingDataAnnotationRequest",
    "DeleteRollupPropertySourceLinkRequest",
    "DeleteSKAdNetworkConversionValueSchemaRequest",
    "DeleteSearchAds360LinkRequest",
    "DeleteSubpropertyEventFilterRequest",
    "DisplayVideo360AdvertiserLink",
    "DisplayVideo360AdvertiserLinkProposal",
    "EnhancedMeasurementSettings",
    "EventCreateRule",
    "EventEditRule",
    "EventMapping",
    "ExpandedDataSet",
    "ExpandedDataSetFilter",
    "ExpandedDataSetFilterExpression",
    "ExpandedDataSetFilterExpressionList",
    "FetchAutomatedGa4ConfigurationOptOutRequest",
    "FetchAutomatedGa4ConfigurationOptOutResponse",
    "FetchConnectedGa4PropertyRequest",
    "FetchConnectedGa4PropertyResponse",
    "FirebaseLink",
    "GetAccessBindingRequest",
    "GetAccountRequest",
    "GetAdSenseLinkRequest",
    "GetAttributionSettingsRequest",
    "GetAudienceRequest",
    "GetBigQueryLinkRequest",
    "GetCalculatedMetricRequest",
    "GetChannelGroupRequest",
    "GetConversionEventRequest",
    "GetCustomDimensionRequest",
    "GetCustomMetricRequest",
    "GetDataRedactionSettingsRequest",
    "GetDataRetentionSettingsRequest",
    "GetDataSharingSettingsRequest",
    "GetDataStreamRequest",
    "GetDisplayVideo360AdvertiserLinkProposalRequest",
    "GetDisplayVideo360AdvertiserLinkRequest",
    "GetEnhancedMeasurementSettingsRequest",
    "GetEventCreateRuleRequest",
    "GetEventEditRuleRequest",
    "GetExpandedDataSetRequest",
    "GetGlobalSiteTagRequest",
    "GetGoogleSignalsSettingsRequest",
    "GetKeyEventRequest",
    "GetMeasurementProtocolSecretRequest",
    "GetPropertyRequest",
    "GetReportingDataAnnotationRequest",
    "GetRollupPropertySourceLinkRequest",
    "GetSKAdNetworkConversionValueSchemaRequest",
    "GetSearchAds360LinkRequest",
    "GetSubpropertyEventFilterRequest",
    "GetSubpropertySyncConfigRequest",
    "GlobalSiteTag",
    "GoogleAdsLink",
    "GoogleSignalsConsent",
    "GoogleSignalsSettings",
    "GoogleSignalsState",
    "GroupingRule",
    "IndustryCategory",
    "KeyEvent",
    "LinkProposalInitiatingProduct",
    "LinkProposalState",
    "LinkProposalStatusDetails",
    "ListAccessBindingsRequest",
    "ListAccessBindingsResponse",
    "ListAccountSummariesRequest",
    "ListAccountSummariesResponse",
    "ListAccountsRequest",
    "ListAccountsResponse",
    "ListAdSenseLinksRequest",
    "ListAdSenseLinksResponse",
    "ListAudiencesRequest",
    "ListAudiencesResponse",
    "ListBigQueryLinksRequest",
    "ListBigQueryLinksResponse",
    "ListCalculatedMetricsRequest",
    "ListCalculatedMetricsResponse",
    "ListChannelGroupsRequest",
    "ListChannelGroupsResponse",
    "ListConnectedSiteTagsRequest",
    "ListConnectedSiteTagsResponse",
    "ListConversionEventsRequest",
    "ListConversionEventsResponse",
    "ListCustomDimensionsRequest",
    "ListCustomDimensionsResponse",
    "ListCustomMetricsRequest",
    "ListCustomMetricsResponse",
    "ListDataStreamsRequest",
    "ListDataStreamsResponse",
    "ListDisplayVideo360AdvertiserLinkProposalsRequest",
    "ListDisplayVideo360AdvertiserLinkProposalsResponse",
    "ListDisplayVideo360AdvertiserLinksRequest",
    "ListDisplayVideo360AdvertiserLinksResponse",
    "ListEventCreateRulesRequest",
    "ListEventCreateRulesResponse",
    "ListEventEditRulesRequest",
    "ListEventEditRulesResponse",
    "ListExpandedDataSetsRequest",
    "ListExpandedDataSetsResponse",
    "ListFirebaseLinksRequest",
    "ListFirebaseLinksResponse",
    "ListGoogleAdsLinksRequest",
    "ListGoogleAdsLinksResponse",
    "ListKeyEventsRequest",
    "ListKeyEventsResponse",
    "ListMeasurementProtocolSecretsRequest",
    "ListMeasurementProtocolSecretsResponse",
    "ListPropertiesRequest",
    "ListPropertiesResponse",
    "ListReportingDataAnnotationsRequest",
    "ListReportingDataAnnotationsResponse",
    "ListRollupPropertySourceLinksRequest",
    "ListRollupPropertySourceLinksResponse",
    "ListSKAdNetworkConversionValueSchemasRequest",
    "ListSKAdNetworkConversionValueSchemasResponse",
    "ListSearchAds360LinksRequest",
    "ListSearchAds360LinksResponse",
    "ListSubpropertyEventFiltersRequest",
    "ListSubpropertyEventFiltersResponse",
    "ListSubpropertySyncConfigsRequest",
    "ListSubpropertySyncConfigsResponse",
    "MatchingCondition",
    "MeasurementProtocolSecret",
    "NumericValue",
    "ParameterMutation",
    "PostbackWindow",
    "Property",
    "PropertySummary",
    "PropertyType",
    "ProvisionAccountTicketRequest",
    "ProvisionAccountTicketResponse",
    "ProvisionSubpropertyRequest",
    "ProvisionSubpropertyResponse",
    "ReorderEventEditRulesRequest",
    "ReportingDataAnnotation",
    "RollupPropertySourceLink",
    "RunAccessReportRequest",
    "RunAccessReportResponse",
    "SKAdNetworkConversionValueSchema",
    "SearchAds360Link",
    "SearchChangeHistoryEventsRequest",
    "SearchChangeHistoryEventsResponse",
    "ServiceLevel",
    "SetAutomatedGa4ConfigurationOptOutRequest",
    "SetAutomatedGa4ConfigurationOptOutResponse",
    "SubmitUserDeletionRequest",
    "SubmitUserDeletionResponse",
    "SubpropertyEventFilter",
    "SubpropertyEventFilterClause",
    "SubpropertyEventFilterCondition",
    "SubpropertyEventFilterExpression",
    "SubpropertyEventFilterExpressionList",
    "SubpropertySyncConfig",
    "UpdateAccessBindingRequest",
    "UpdateAccountRequest",
    "UpdateAttributionSettingsRequest",
    "UpdateAudienceRequest",
    "UpdateBigQueryLinkRequest",
    "UpdateCalculatedMetricRequest",
    "UpdateChannelGroupRequest",
    "UpdateConversionEventRequest",
    "UpdateCustomDimensionRequest",
    "UpdateCustomMetricRequest",
    "UpdateDataRedactionSettingsRequest",
    "UpdateDataRetentionSettingsRequest",
    "UpdateDataStreamRequest",
    "UpdateDisplayVideo360AdvertiserLinkRequest",
    "UpdateEnhancedMeasurementSettingsRequest",
    "UpdateEventCreateRuleRequest",
    "UpdateEventEditRuleRequest",
    "UpdateExpandedDataSetRequest",
    "UpdateGoogleAdsLinkRequest",
    "UpdateGoogleSignalsSettingsRequest",
    "UpdateKeyEventRequest",
    "UpdateMeasurementProtocolSecretRequest",
    "UpdatePropertyRequest",
    "UpdateReportingDataAnnotationRequest",
    "UpdateSKAdNetworkConversionValueSchemaRequest",
    "UpdateSearchAds360LinkRequest",
    "UpdateSubpropertyEventFilterRequest",
    "UpdateSubpropertySyncConfigRequest",
)
